document.addEventListener('DOMContentLoaded', () => {
    // Mobile Menu Functionality
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('nav ul');
    
    mobileMenuBtn.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        // Toggle menu button animation
        mobileMenuBtn.classList.toggle('active');
    });

    // Scroll to Top Button
    const scrollTopBtn = document.querySelector('.scroll-top');
    
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            scrollTopBtn.style.opacity = '1';
        } else {
            scrollTopBtn.style.opacity = '0';
        }
    });

    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // GSAP Animations
    gsap.from(".hero h1", {
        duration: 1,
        y: 20,
        opacity: 0,
        delay: 0.5
    });

    gsap.from(".hero p", {
        duration: 1,
        y: 20,
        opacity: 0,
        delay: 0.8
    });

    gsap.from(".hero .button", {
        duration: 1,
        y: 20,
        opacity: 0,
        delay: 1
    });

    // Service Cards Animation
    gsap.from(".service-card", {
        scrollTrigger: {
            trigger: ".services-grid",
            start: "top center+=100",
            toggleActions: "play none none none"
        },
        y: 50,
        opacity: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "power2.out"
    });

    // About Section Animation
    gsap.from(".about-content", {
        scrollTrigger: {
            trigger: ".about-content",
            start: "top center",
            toggleActions: "play none none reverse"
        },
        duration: 1,
        y: 30,
        opacity: 0
    });

    // Form Animation
    const form = document.querySelector('.contact-form');
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Animate form on submit
        gsap.to(form, {
            scale: 0.95,
            duration: 0.1,
            yoyo: true,
            repeat: 1,
            onComplete: () => {
                // Reset form after animation
                form.reset();
                
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'success-message';
                successMessage.textContent = 'Message sent successfully!';
                successMessage.style.color = 'white';
                successMessage.style.marginTop = '1rem';
                form.appendChild(successMessage);
                
                // Remove success message after 3 seconds
                setTimeout(() => {
                    successMessage.remove();
                }, 3000);
            }
        });
    });

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            
            if (target) {
                // Close mobile menu if open
                if (navMenu.classList.contains('active')) {
                    navMenu.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                }

                // Smooth scroll to target
                window.scrollTo({
                    top: target.offsetTop - 60, // Adjust for fixed header
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add scroll class to nav for background change
    window.addEventListener('scroll', () => {
        const nav = document.querySelector('nav');
        if (window.scrollY > 50) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    });

    // Initialize loading animations
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');
    });
});

function sendEmail(event) {
    event.preventDefault();
    
    const form = document.getElementById('contactForm');
    if (!form) {
        console.error('Contact form not found');
        return;
    }

    const name = form.elements[0].value;
    const email = form.elements[1].value;
    const subject = form.elements[2].value;
    const message = form.elements[3].value;

    // Add console log for debugging
    console.log('Sending email with:', { name, email, subject, message });

    // Construct mailto URL with all parameters
    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(
        `Name: ${name}\nEmail: ${email}\n\nMessage:\n${message}`
    )}`;

    // Open default email client
    window.location.href = mailtoUrl;

    // Show success message
    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.textContent = 'Opening your email client...';
    successMessage.style.color = 'white';
    successMessage.style.marginTop = '1rem';
    form.appendChild(successMessage);

    // Remove success message after 3 seconds
    setTimeout(() => {
        successMessage.remove();
        form.reset();
    }, 3000);
}
