:root {
    --primary: #2563eb;
    --secondary: #1e40af;
    --text: #1f2937;
    --light: #f3f4f6;
    --dark: #111827;
    --transition: all 0.3s ease;
}

/* General Styles */
body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    color: #333;
    margin: 0;
    padding: 0;
}

/* Ensure section is visible */
.our-services {
    display: block;
    visibility: visible;
    padding: 40px;
    background-color: #ffffff;
    text-align: center;
}

/* Headings */
.our-services h2 {
    font-size: 32px;
    color: #222;
    margin-bottom: 20px;
}

/* Service List */
.service-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px; /* Space between items */
}

/* Service Items */
.service-item {
    flex: 1 1 300px; /* Allow multiple items to be displayed */
    max-width: 400px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: left;
    min-height: 180px; /* Ensure all blocks have enough height */
}

/* Ensure text is visible */
.service-item h3 {
    font-size: 24px;
    color: #111;
}

.service-item p {
    font-size: 16px;
    color: #555;
    line-height: 1.6;
}

/* Projects Section Styling */
/* Projects Section Styling - Improved */
.projects {
    background-color: #f5f7fa;
    padding: 6rem 2rem;
    position: relative;
}

.projects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.8;
    z-index: 0;
}

.projects .container {
    position: relative;
    z-index: 1;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
}

/* Enhanced project card styling */
.project-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.06);
    overflow: hidden;
    transition: all 0.4s ease;
    background: white;
    border: 1px solid rgba(37, 99, 235, 0.1);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(37, 99, 235, 0.15);
    border-color: rgba(37, 99, 235, 0.2);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.08);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 64, 175, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-overlay .button {
    background: white;
    color: var(--primary);
    transform: translateY(20px);
    transition: all 0.4s ease;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.project-card:hover .project-overlay .button {
    transform: translateY(0);
}

.project-details {
    padding: 1.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.project-details h3 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    color: var(--text);
    font-weight: 700;
}

.project-details p {
    color: #555;
    margin-bottom: 1.25rem;
    line-height: 1.6;
}

.project-details h4 {
    font-size: 1.1rem;
    color: var(--text);
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.project-features {
    margin: 1rem 0;
}

.project-features ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 1.5rem;
}

.project-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: #4b5563;
}

.project-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--primary);
    font-weight: bold;
}

.project-tech {
    margin-top: 0.5rem;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.project-tags span {
    background: rgba(37, 99, 235, 0.08);
    color: var(--primary);
    padding: 0.35rem 0.85rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.project-tags span:hover {
    background: rgba(37, 99, 235, 0.15);
}

.project-links {
    margin-top: auto;
    padding-top: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.button-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1.2rem;
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary);
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.button-secondary:hover {
    background-color: rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.button-secondary i {
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-links {
        flex-direction: column;
    }
    
    .button-secondary {
        width: 100%;
        justify-content: center;
    }
}
/* Responsive adjustments */
@media (max-width: 992px) {
    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .service-list {
        flex-direction: column;
        align-items: center;
    }

    .service-item {
        width: 90%;
    }
}


/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: system-ui, -apple-system, sans-serif;
}

html {
    scroll-behavior: smooth;
}

body {
    color: var(--text);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation */
nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary);
}

nav ul {
    display: flex;
    gap: 2rem;
    list-style: none;
}

nav a {
    text-decoration: none;
    color: var(--text);
    font-weight: 500;
    transition: var(--transition);
}

nav a:hover {
    color: var(--primary);
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: 6px;
    cursor: pointer;
    padding: 10px;
}

.mobile-menu-btn span {
    width: 25px;
    height: 2px;
    background: var(--text);
    transition: var(--transition);
}

.mobile-menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(8px, 8px);
}

.mobile-menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 8rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: 60px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.25rem;
    max-width: 600px;
    margin: 0 auto 2rem auto;
}

/* Services Section */
section {
    padding: 5rem 2rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.services {
  border: 2px solid rgb(255, 255, 255) !important;
  background-color: rgb(255, 255, 255) !important;
}

.service-card {
  opacity: 1 !important;
  transform: translateY(0) !important;
  border: 2px solid blue !important;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 0 auto;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    opacity: 1;
    transform: translateY(0);
}

.service-card i {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
    display: block;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text);
}

.service-card p {
    color: var(--text);
    line-height: 1.6;
    margin: 0;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card i {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--text);
}

/* About Section */
.about {
    max-width: 1000px;
    margin: 0 auto;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-image img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* Contact Section */
.cta {
    text-align: center;
    background: var(--primary);
    color: white;
}

.contact-form {
    max-width: 600px;
    margin: 2rem auto;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.contact-form input::placeholder,
.contact-form textarea::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.contact-form select {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
}

.contact-form select option {
    background: var(--primary);
    color: white;
}

.contact-form select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
}

/* Style for the placeholder option */
.contact-form select option[value=""] {
    color: rgba(255, 255, 255, 0.7);
}

.button {
    display: inline-block;
    padding: 1rem 2rem;
    background: white;
    color: var(--primary);
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.button:hover {
    transform: scale(1.05);
}

/* Footer */
footer {
    background: var(--dark);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
}

.social-links {
    margin: 2rem 0;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
    margin: 0 1rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary);
}

/* Scroll to Top */
.scroll-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: var(--transition);
    z-index: 99;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
    }

    nav ul {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
        transform: translateY(-150%);
        transition: var(--transition);
    }

    nav ul.active {
        transform: translateY(0);
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .hero h1 {
        font-size: 2.5rem;
    }
}

